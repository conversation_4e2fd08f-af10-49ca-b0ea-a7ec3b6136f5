<Control xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
         xmlns:robustControls="clr-namespace:Robust.Client.UserInterface.Controls;assembly=Robust.Client"
         xmlns:s="clr-namespace:Content.Client.Stylesheets">
    <BoxContainer Orientation="Vertical">
        <ScrollContainer VerticalExpand="True" HorizontalExpand="True">
            <BoxContainer Orientation="Vertical" Margin="8 8 8 8" VerticalExpand="True">
                <!-- Name Highlighting Section -->
                <Label Text="{Loc 'ui-options-pirate-ping-name-title'}"
                       FontColorOverride="{x:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="PingNameEnabledCheckBox" Text="{Loc 'ui-options-pirate-ping-name-enabled'}" />
                <CheckBox Name="PingNameSoundsCheckBox" Text="{Loc 'ui-options-pirate-ping-name-sound-enabled'}" />
                <CheckBox Name="PingNameSelfSoundsCheckBox" Text="{Loc 'ui-options-pirate-ping-name-self-sound-enabled'}" />

                <!-- Sound Selection -->
                <Label Text="{Loc 'ui-options-pirate-ping-name-sound-selection'}"
                       FontColorOverride="{x:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'ui-options-pirate-ping-name-sound'}" />
                    <Control MinSize="4 0" />
                    <OptionButton Name="PingSoundSelector" />
                    <Control MinSize="4 0" />
                    <Button Name="TestSoundButton" Text="{Loc 'ui-options-pirate-ping-name-test-sound'}" />
                </BoxContainer>

                <!-- Sound Cooldown -->
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'ui-options-pirate-ping-name-sound-cooldown'}" Margin="8 0" />
                    <FloatSpinBox Name="SoundCooldownSpinBox" MinWidth="100" Value="15"/>
                    <Label Text="{Loc 'ui-options-pirate-ping-name-sound-cooldown-suffix'}" Margin="8 0" />
                </BoxContainer>

                <!-- Sound Volume -->
                <BoxContainer Orientation="Horizontal">
                    <Label Text="{Loc 'ui-options-pirate-ping-name-sound-volume'}" Margin="8 0" />
                    <Slider Name="SoundVolumeSlider" MinValue="0" MaxValue="1" Value="0.5" HorizontalExpand="True" />
                    <Label Name="SoundVolumeLabel" Text="50%" MinWidth="40" Margin="8 0" />
                </BoxContainer>

                <!-- Custom Words -->
                <Label Text="{Loc 'ui-options-pirate-ping-name-custom-words'}"
                       FontColorOverride="{x:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <BoxContainer Orientation="Vertical">
                    <LineEdit Name="CustomWordsLineEdit"
                             PlaceHolder="{Loc 'ui-options-pirate-ping-name-custom-words-placeholder'}"/>
                    <Label Name="CustomWordsLimitLabel"
                           Text="Залишилось символів: 250/250"
                           HorizontalAlignment="Right"
                           Margin="0 2 0 0"
                           StyleClasses="LabelSubText"/>
                </BoxContainer>

                <!-- Color Settings -->
                <Label Text="{Loc 'ui-options-pirate-ping-name-color'}"
                       FontColorOverride="{x:Static s:StyleNano.NanoGold}"
                       StyleClasses="LabelKeyText"/>
                <BoxContainer Orientation="Horizontal">
                    <BoxContainer Orientation="Vertical" MinSize="200 0">
                        <Label Text="{Loc 'ui-options-pirate-ping-name-color-preview'}" />
                        <PanelContainer Name="PingNameColorPreview"
                               MinSize="30 30"
                               ToolTip="{Loc 'ui-options-pirate-ping-name-color-preview-tooltip'}"/>
                    </BoxContainer>
                    <Control MinSize="8 0" />
                    <robustControls:ColorSelectorSliders Name="ColorSliders" HorizontalExpand="True" />
                </BoxContainer>
                <Button Name="PingNameColorReset"
                       Text="{Loc 'ui-options-pirate-ping-name-color-reset'}"
                       HorizontalAlignment="Center" />
            </BoxContainer>
        </ScrollContainer>
        <controls:StripeBack HasBottomEdge="False" HasMargins="False">
            <BoxContainer Orientation="Horizontal" Align="End" HorizontalExpand="True" VerticalExpand="True">
                <Button Name="ResetButton" Text="{Loc 'ui-options-reset-all'}" StyleClasses="Danger" HorizontalExpand="True" HorizontalAlignment="Right" />
                <Control MinSize="2 0" />
                <Button Name="ApplyButton" Text="{Loc 'ui-options-apply'}" TextAlign="Center" HorizontalAlignment="Right" />
            </BoxContainer>
        </controls:StripeBack>
    </BoxContainer>
</Control>
